{"model_name": "Optimized Random Forest", "training_date": "2025-09-05T01:21:13.524821", "cv_accuracy_mean": 1.0, "cv_accuracy_std": 0.0, "training_accuracy": 1.0, "auc_score": 1.0, "feature_columns": ["temperature", "ph", "dissolved_oxygen", "bod", "nitrate_n", "fecal_coliform", "total_coliform", "fecal_streptococci", "water_quality_risk_score", "contamination_load", "pollution_index", "ph_oxygen_balance", "severe_contamination", "extreme_ph", "oxygen_depletion", "total_risk_score"], "total_samples": 54, "original_samples": 4, "synthetic_samples": 50, "model_params": {"n_estimators": 150, "max_depth": 8, "min_samples_split": 8, "min_samples_leaf": 3, "random_state": 42, "class_weight": "balanced"}, "version": "6.0"}