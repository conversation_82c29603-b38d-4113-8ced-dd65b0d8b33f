# -*- coding: utf-8 -*-
"""
Optimized Waterborne Disease Prediction Model
Production-ready model with easy retraining capabilities
"""

import pandas as pd
import numpy as np
import joblib
import json
from datetime import datetime
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
from sklearn.impute import SimpleImputer

class WaterborneDiseasePredictionModel:
    """
    Optimized model for waterborne disease prediction in Northeast India
    """
    
    def __init__(self, model_dir="./"):
        self.model_dir = Path(model_dir)
        self.model = None
        self.scaler = None
        self.imputer = None
        self.feature_columns = None
        self.metadata = {}
        
        # Best performing hyperparameters (from previous experiments)
        self.model_params = {
            'n_estimators': 150,
            'max_depth': 8,
            'min_samples_split': 8,
            'min_samples_leaf': 3,
            'random_state': 42,
            'class_weight': 'balanced'
        }
    
    def load_data(self, disease_file='northeast_states_disease_outbreaks.csv', 
                  water_file='northeast_water_quality_data.csv'):
        """Load and preprocess the original data"""
        print("📁 Loading data...")
        
        # Load disease data
        disease_df = pd.read_csv(self.model_dir / disease_file)
        
        # Load water quality data
        water_df = pd.read_csv(self.model_dir / water_file)
        
        # Process disease data
        waterborne_diseases = ['Acute Diarrheal Disease', 'Cholera', 'Hepatitis A', 
                              'Acute Diarrheal Diseases', 'Acute Diarrhoeal Disease']
        disease_df['is_waterborne'] = disease_df['Disease_Illness'].isin(waterborne_diseases).astype(int)
        
        # Select numeric columns for water quality
        numeric_cols = ['temperature', 'ph', 'dissolved_oxygen', 'bod', 'nitrate_n', 
                       'fecal_coliform', 'total_coliform', 'fecal_streptococci']
        
        water_clean = water_df[['northeast_state'] + numeric_cols].copy()
        
        # Convert to numeric and handle missing values
        for col in numeric_cols:
            water_clean[col] = pd.to_numeric(water_clean[col], errors='coerce')
        
        # Create water quality risk indicators
        water_clean['ph_risk'] = ((water_clean['ph'] < 6.5) | (water_clean['ph'] > 8.5)).astype(int)
        water_clean['do_risk'] = (water_clean['dissolved_oxygen'] < 5).astype(int)
        water_clean['bod_risk'] = (water_clean['bod'] > 5).astype(int)
        water_clean['nitrate_risk'] = (water_clean['nitrate_n'] > 10).astype(int)
        water_clean['fecal_coliform_risk'] = (water_clean['fecal_coliform'] > 0).astype(int)
        water_clean['total_coliform_risk'] = (water_clean['total_coliform'] > 0).astype(int)
        
        water_clean['water_quality_risk_score'] = (
            water_clean['ph_risk'] + water_clean['do_risk'] + water_clean['bod_risk'] + 
            water_clean['nitrate_risk'] + water_clean['fecal_coliform_risk'] + water_clean['total_coliform_risk']
        )
        
        # Aggregate by state
        disease_by_state = disease_df.groupby('Northeast_State')['is_waterborne'].sum().reset_index()
        disease_by_state['has_waterborne_outbreak'] = (disease_by_state['is_waterborne'] > 0).astype(int)
        
        water_by_state = water_clean.groupby('northeast_state')[numeric_cols + ['water_quality_risk_score']].mean().reset_index()
        
        # Merge datasets
        merged_df = pd.merge(disease_by_state, water_by_state, 
                            left_on='Northeast_State', right_on='northeast_state', how='inner')
        
        final_df = merged_df[numeric_cols + ['water_quality_risk_score', 'has_waterborne_outbreak']].copy()
        
        print(f"✅ Loaded {len(final_df)} original samples")
        return final_df
    
    def generate_synthetic_data(self, original_df, n_samples=50):
        """Generate high-quality synthetic training data"""
        print(f"🔄 Generating {n_samples} synthetic samples...")
        
        synthetic_data = []
        
        for i in range(n_samples):
            # 70% outbreak samples for better learning
            create_outbreak = np.random.random() < 0.7
            
            if create_outbreak:
                # Contaminated water sample
                sample = {
                    'temperature': np.clip(np.random.normal(24, 3), 15, 35),
                    'ph': np.clip(np.random.choice([np.random.normal(5.5, 0.5), np.random.normal(8.8, 0.3)]), 5.5, 9.0),
                    'dissolved_oxygen': np.clip(np.random.exponential(3) + 1, 1, 15),
                    'bod': np.clip(np.random.exponential(8) + 5, 1, 50),
                    'nitrate_n': np.clip(np.random.exponential(5) + 2, 0.1, 25),
                    'fecal_coliform': np.clip(np.random.exponential(80) + 10, 0, 1000),
                    'fecal_streptococci': np.clip(np.random.exponential(25) + 5, 0, 500),
                    'has_waterborne_outbreak': 1
                }
                sample['total_coliform'] = np.clip(sample['fecal_coliform'] + np.random.exponential(150) + 20, sample['fecal_coliform'], 5000)
            else:
                # Clean water sample
                sample = {
                    'temperature': np.clip(np.random.normal(20, 2), 15, 35),
                    'ph': np.clip(np.random.normal(7.2, 0.4), 5.5, 9.0),
                    'dissolved_oxygen': np.clip(np.random.normal(8, 1.5) + 5, 1, 15),
                    'bod': np.clip(np.random.exponential(2) + 1, 1, 50),
                    'nitrate_n': np.clip(np.random.exponential(1.5) + 0.5, 0.1, 25),
                    'fecal_coliform': np.clip(np.random.exponential(3) if np.random.random() < 0.3 else 0, 0, 1000),
                    'fecal_streptococci': np.clip(np.random.exponential(2) if np.random.random() < 0.2 else 0, 0, 500),
                    'has_waterborne_outbreak': 0
                }
                sample['total_coliform'] = np.clip(sample['fecal_coliform'] + np.random.exponential(10) + 2, sample['fecal_coliform'], 5000)
            
            # Calculate risk score
            ph_risk = 1 if (sample['ph'] < 6.5 or sample['ph'] > 8.5) else 0
            do_risk = 1 if sample['dissolved_oxygen'] < 5 else 0
            bod_risk = 1 if sample['bod'] > 5 else 0
            nitrate_risk = 1 if sample['nitrate_n'] > 10 else 0
            fecal_coliform_risk = 1 if sample['fecal_coliform'] > 0 else 0
            total_coliform_risk = 1 if sample['total_coliform'] > 0 else 0
            
            sample['water_quality_risk_score'] = (ph_risk + do_risk + bod_risk + 
                                                nitrate_risk + fecal_coliform_risk + total_coliform_risk)
            
            synthetic_data.append(sample)
        
        synthetic_df = pd.DataFrame(synthetic_data)
        print(f"✅ Generated synthetic data with {synthetic_df['has_waterborne_outbreak'].mean():.1%} outbreak rate")
        return synthetic_df
    
    def engineer_features(self, df):
        """Add optimized engineered features"""
        print("🔧 Engineering features...")
        
        # Key interaction features that proved most effective
        df['contamination_load'] = df['fecal_coliform'] + df['total_coliform'] + df['fecal_streptococci']
        df['pollution_index'] = df['bod'] * df['nitrate_n'] / (df['dissolved_oxygen'] + 1)
        df['ph_oxygen_balance'] = df['ph'] * df['dissolved_oxygen']
        
        # Critical risk indicators
        df['severe_contamination'] = (df['contamination_load'] > 200).astype(int)
        df['extreme_ph'] = ((df['ph'] < 6) | (df['ph'] > 9)).astype(int)
        df['oxygen_depletion'] = (df['dissolved_oxygen'] < 4).astype(int)
        
        # Composite risk score
        df['total_risk_score'] = (df['water_quality_risk_score'] + df['severe_contamination'] + 
                                 df['extreme_ph'] + df['oxygen_depletion'])
        
        print("✅ Added 7 engineered features")
        return df
    
    def prepare_features(self, df):
        """Prepare final feature set"""
        self.feature_columns = [
            'temperature', 'ph', 'dissolved_oxygen', 'bod', 'nitrate_n', 
            'fecal_coliform', 'total_coliform', 'fecal_streptococci', 'water_quality_risk_score',
            'contamination_load', 'pollution_index', 'ph_oxygen_balance',
            'severe_contamination', 'extreme_ph', 'oxygen_depletion', 'total_risk_score'
        ]
        
        X = df[self.feature_columns]
        y = df['has_waterborne_outbreak']
        
        return X, y
    
    def train(self, disease_file=None, water_file=None, n_synthetic=50, save_model=True):
        """Train the optimized model"""
        print("🚀 Training Optimized Waterborne Disease Prediction Model")
        print("=" * 60)
        
        # Load and prepare data
        original_df = self.load_data(disease_file, water_file) if disease_file and water_file else self.load_data()
        synthetic_df = self.generate_synthetic_data(original_df, n_synthetic)
        
        # Combine datasets
        combined_df = pd.concat([original_df, synthetic_df], ignore_index=True)
        enhanced_df = self.engineer_features(combined_df)
        
        # Prepare features
        X, y = self.prepare_features(enhanced_df)
        
        print(f"\n📊 Training Dataset:")
        print(f"Total samples: {len(enhanced_df)} (Original: {len(original_df)}, Synthetic: {len(synthetic_df)})")
        print(f"Features: {len(self.feature_columns) if self.feature_columns else 0}")
        print(f"Outbreak rate: {y.mean():.1%}")
        
        # Handle missing values
        self.imputer = SimpleImputer(strategy='median')
        X_imputed = self.imputer.fit_transform(X)
        X = pd.DataFrame(X_imputed, columns=self.feature_columns)
        
        # Scale features
        self.scaler = RobustScaler()
        X_scaled = self.scaler.fit_transform(X)
        
        # Train model
        print("\n🤖 Training Random Forest model...")
        self.model = RandomForestClassifier(**self.model_params)
        self.model.fit(X, y)  # Use original features for Random Forest
        
        # Evaluate with cross-validation
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        cv_scores = cross_val_score(self.model, X, y, cv=cv, scoring='accuracy')
        
        # Calculate additional metrics
        y_pred = self.model.predict(X)
        y_pred_proba = self.model.predict_proba(X)[:, 1]
        
        accuracy = accuracy_score(y, y_pred)
        auc = roc_auc_score(y, y_pred_proba)
        
        print(f"\n📈 Model Performance:")
        print(f"Cross-validation Accuracy: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
        print(f"Training Accuracy: {accuracy:.3f}")
        print(f"AUC Score: {auc:.3f}")
        
        # Feature importance
        feature_importance = pd.DataFrame({
            'feature': self.feature_columns,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"\n🔍 Top 5 Most Important Features:")
        for _, row in feature_importance.head(5).iterrows():
            print(f"{row['feature']:25} | {row['importance']:.4f}")
        
        # Store metadata
        self.metadata = {
            'model_name': 'Optimized Random Forest',
            'training_date': datetime.now().isoformat(),
            'cv_accuracy_mean': float(cv_scores.mean()),
            'cv_accuracy_std': float(cv_scores.std()),
            'training_accuracy': float(accuracy),
            'auc_score': float(auc),
            'feature_columns': self.feature_columns,
            'total_samples': len(enhanced_df),
            'original_samples': len(original_df),
            'synthetic_samples': len(synthetic_df),
            'model_params': self.model_params,
            'version': '6.0'
        }
        
        if save_model:
            self.save_model()
        
        print(f"\n✅ Model training completed!")
        print(f"Final Accuracy: {cv_scores.mean():.1%}")
        
        return self.metadata
    
    def predict(self, water_params):
        """Make prediction for given water quality parameters"""
        if self.model is None:
            raise ValueError("Model not trained or loaded. Call train() or load_model() first.")

        if self.imputer is None:
            raise ValueError("Imputer not initialized. Call train() or load_model() first.")

        if self.scaler is None:
            raise ValueError("Scaler not initialized. Call train() or load_model() first.")

        if self.feature_columns is None:
            raise ValueError("Feature columns not defined. Call train() or load_model() first.")

        # Convert input to DataFrame
        if isinstance(water_params, dict):
            # Single prediction
            input_df = pd.DataFrame([water_params])
        else:
            # Multiple predictions
            input_df = pd.DataFrame(water_params)

        # Engineer features for input
        input_df = self.engineer_features(input_df)

        # Select features and handle missing values
        X = input_df[self.feature_columns]
        # Convert DataFrame to numpy array for sklearn compatibility
        X_imputed = self.imputer.transform(X.values)
        # Ensure X_imputed is a numpy array (handle sparse matrices)
        X_imputed = np.asarray(X_imputed)
        X = pd.DataFrame(X_imputed, columns=self.feature_columns)

        # Make prediction (Random Forest doesn't need scaling, but keeping for consistency)
        prediction = self.model.predict(X)
        probability = self.model.predict_proba(X)[:, 1]
        
        # Return results
        if len(X) == 1:
            return {
                'prediction': int(prediction[0]),
                'probability': float(probability[0]),
                'risk_level': self._get_risk_level(probability[0])
            }
        else:
            return {
                'predictions': prediction.tolist(),
                'probabilities': probability.tolist(),
                'risk_levels': [self._get_risk_level(p) for p in probability]
            }
    
    def _get_risk_level(self, probability):
        """Convert probability to risk level"""
        if probability < 0.3:
            return "Low"
        elif probability < 0.7:
            return "Medium"
        else:
            return "High"
    
    def save_model(self, filename_prefix='optimized_waterborne_model'):
        """Save the trained model"""
        model_file = self.model_dir / f"{filename_prefix}.pkl"
        scaler_file = self.model_dir / f"{filename_prefix}_scaler.pkl"
        imputer_file = self.model_dir / f"{filename_prefix}_imputer.pkl"
        metadata_file = self.model_dir / f"{filename_prefix}_metadata.json"
        
        joblib.dump(self.model, model_file)
        joblib.dump(self.scaler, scaler_file)
        joblib.dump(self.imputer, imputer_file)
        
        with open(metadata_file, 'w') as f:
            json.dump(self.metadata, f, indent=2)
        
        print(f"💾 Model saved:")
        print(f"  - Model: {model_file}")
        print(f"  - Scaler: {scaler_file}")
        print(f"  - Imputer: {imputer_file}")
        print(f"  - Metadata: {metadata_file}")
    
    def load_model(self, filename_prefix='optimized_waterborne_model'):
        """Load a trained model"""
        model_file = self.model_dir / f"{filename_prefix}.pkl"
        scaler_file = self.model_dir / f"{filename_prefix}_scaler.pkl"
        imputer_file = self.model_dir / f"{filename_prefix}_imputer.pkl"
        metadata_file = self.model_dir / f"{filename_prefix}_metadata.json"
        
        self.model = joblib.load(model_file)
        self.scaler = joblib.load(scaler_file)
        self.imputer = joblib.load(imputer_file)
        
        with open(metadata_file, 'r') as f:
            self.metadata = json.load(f)
        
        self.feature_columns = self.metadata['feature_columns']
        
        print(f"✅ Model loaded successfully!")
        print(f"Model: {self.metadata['model_name']}")
        print(f"Accuracy: {self.metadata['cv_accuracy_mean']:.3f}")
        print(f"Training date: {self.metadata['training_date']}")

# Example usage and testing
if __name__ == "__main__":
    # Initialize model
    model = WaterborneDiseasePredictionModel()
    
    # Train the model
    results = model.train(n_synthetic=50)
    
    # Test prediction
    test_sample = {
        'temperature': 25,
        'ph': 4.5,
        'dissolved_oxygen': 3,
        'bod': 15,
        'nitrate_n': 15,
        'fecal_coliform': 200,
        'total_coliform': 1000,
        'fecal_streptococci': 50,
        'water_quality_risk_score': 6
    }
    
    prediction = model.predict(test_sample)
    print(f"\n🧪 Test Prediction:")
    print(f"Risk Level: {prediction['risk_level']}")
    print(f"Probability: {prediction['probability']:.3f}")
    print(f"Prediction: {'Outbreak Risk' if prediction['prediction'] == 1 else 'No Risk'}")
